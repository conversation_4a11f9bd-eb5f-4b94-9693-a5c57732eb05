'use client';

import { useMemo, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useBrandingStore } from '@/store/brandingStore';
import { ShoppingCart, Star, Heart } from 'lucide-react';

// FontPreviewer component for dynamic Google Fonts loading
function FontPreviewer({ fontName, fontWeight = "400", children }: {
  fontName: string;
  fontWeight?: string;
  children: React.ReactNode;
}) {
  useEffect(() => {
    if (!fontName || fontName === 'inherit') return;

    const fontParam = fontName.replace(/ /g, '+');
    const linkId = `dynamic-google-font-${fontParam}`;

    // Check if font is already loaded
    if (document.getElementById(linkId)) return;

    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = `https://fonts.googleapis.com/css2?family=${fontParam}`;
    link.id = linkId;
    document.head.appendChild(link);

    return () => {
      const existingLink = document.getElementById(linkId);
      if (existingLink) {
        document.head.removeChild(existingLink);
      }
    };
  }, [fontName, fontWeight]);

  return (
    <div style={{ fontFamily: `'${fontName}', sans-serif` }}>
      {children}
    </div>
  );
}

export function VisualPreviewSection() {
  const {
    businessInput,
    brandColors,
    brandFonts,
    graphicAssets,
    productInfo
  } = useBrandingStore();

  // Generate dynamic CSS styles based on state
  const dynamicStyles = useMemo(() => {
    const baseStyles = {
      primaryColor: brandColors.primary,
      secondaryColor: brandColors.secondary,
      accentColor: brandColors.accent,
      backgroundColor: brandColors.background,
      textColor: brandColors.text,
      primaryFont: brandFonts.primary,
      secondaryFont: brandFonts.secondary,
    };

    const gradientStyle = graphicAssets.useGradients
      ? `linear-gradient(135deg, ${brandColors.primary}, ${brandColors.secondary})`
      : brandColors.primary;

    const glowEffect = graphicAssets.useGlowEffects
      ? `0 0 20px ${brandColors.primary}40`
      : 'none';

    const borderStyle = graphicAssets.useThickBorders
      ? `3px solid ${brandColors.accent}`
      : `1px solid ${brandColors.primary}20`;

    return {
      ...baseStyles,
      gradientStyle,
      glowEffect,
      borderStyle,
    };
  }, [brandColors, brandFonts, graphicAssets]);

  const TypographyCard = () => (
    <Card 
      className="mb-6 overflow-hidden transition-all duration-300"
      style={{
        background: dynamicStyles.gradientStyle,
        boxShadow: dynamicStyles.glowEffect,
        border: dynamicStyles.borderStyle
      }}
    >
      <CardContent className="p-8 text-center">
        <h1 
          className="text-4xl font-bold mb-4 transition-all duration-300"
          style={{
            fontFamily: dynamicStyles.primaryFont,
            color: brandColors.background,
            textShadow: graphicAssets.useGlowEffects ? `0 0 10px ${brandColors.background}50` : 'none',
          }}
        >
          {businessInput.name || 'Your Business Name'}
        </h1>
        <p 
          className="text-lg opacity-90 transition-all duration-300"
          style={{
            fontFamily: dynamicStyles.secondaryFont,
            color: brandColors.background,
          }}
        >
          {businessInput.description || 'Your business description will appear here'}
        </p>
      </CardContent>
    </Card>
  );

  const WebsiteCard = () => (
    <Card 
      className="overflow-hidden transition-all duration-300"
      style={{
        border: dynamicStyles.borderStyle
      }}
    >
      <CardContent className="p-0">
        {/* Mock Website Header */}
        <div 
          className="p-4 transition-all duration-300"
          style={{
            background: graphicAssets.useGradients 
              ? `linear-gradient(90deg, ${brandColors.primary}, ${brandColors.secondary})`
              : brandColors.primary,
          }}
        >
          <div className="flex items-center justify-between">
            <h2 
              className="text-xl font-bold transition-all duration-300"
              style={{
                fontFamily: dynamicStyles.primaryFont,
                color: brandColors.background,
              }}
            >
              {businessInput.name || 'Business'}
            </h2>
            <div className="flex space-x-4">
              <span 
                className="text-sm transition-all duration-300"
                style={{
                  fontFamily: dynamicStyles.secondaryFont,
                  color: brandColors.background,
                }}
              >
                Home
              </span>
              <span 
                className="text-sm transition-all duration-300"
                style={{
                  fontFamily: dynamicStyles.secondaryFont,
                  color: brandColors.background,
                }}
              >
                Products
              </span>
              <span 
                className="text-sm transition-all duration-300"
                style={{
                  fontFamily: dynamicStyles.secondaryFont,
                  color: brandColors.background,
                }}
              >
                Contact
              </span>
            </div>
          </div>
        </div>

        {/* Mock Product Card */}
        <div className="p-6">
          <div 
            className="rounded-lg p-6 transition-all duration-300"
            style={{
              background: brandColors.background,
              border: `1px solid ${brandColors.primary}20`,
              boxShadow: graphicAssets.useGlowEffects 
                ? `0 4px 20px ${brandColors.primary}20`
                : '0 2px 10px rgba(0,0,0,0.1)',
            }}
          >
            {/* Product Image Placeholder */}
            <div 
              className="w-full h-32 rounded-lg mb-4 transition-all duration-300"
              style={{
                background: graphicAssets.useGradients
                  ? `linear-gradient(45deg, ${brandColors.secondary}, ${brandColors.accent})`
                  : brandColors.secondary,
              }}
            />

            {/* Product Info */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 
                  className="text-lg font-semibold transition-all duration-300"
                  style={{
                    fontFamily: dynamicStyles.primaryFont,
                    color: brandColors.text,
                  }}
                >
                  {productInfo.name}
                </h3>
                <div className="flex items-center space-x-1">
                  {[...Array(5)].map((_, i) => (
                    <Star 
                      key={i} 
                      className="h-4 w-4 fill-current transition-all duration-300"
                      style={{ color: brandColors.accent }}
                    />
                  ))}
                </div>
              </div>

              <p 
                className="text-sm transition-all duration-300"
                style={{
                  fontFamily: dynamicStyles.secondaryFont,
                  color: `${brandColors.text}80`,
                }}
              >
                {productInfo.description}
              </p>

              {/* Variants */}
              <div className="flex flex-wrap gap-2">
                {productInfo.variants.map((variant, index) => (
                  <Badge 
                    key={index}
                    variant="outline"
                    className="transition-all duration-300"
                    style={{
                      borderColor: brandColors.primary,
                      color: brandColors.primary,
                    }}
                  >
                    {variant}
                  </Badge>
                ))}
              </div>

              {/* Price and Actions */}
              <div className="flex items-center justify-between pt-2">
                <span 
                  className="text-2xl font-bold transition-all duration-300"
                  style={{
                    fontFamily: dynamicStyles.primaryFont,
                    color: brandColors.primary,
                  }}
                >
                  {productInfo.price}
                </span>
                <div className="flex space-x-2">
                  <Button 
                    size="sm" 
                    variant="outline"
                    className="transition-all duration-300"
                    style={{
                      borderColor: brandColors.primary,
                      color: brandColors.primary,
                    }}
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                  <Button 
                    size="sm"
                    className="transition-all duration-300"
                    style={{
                      background: graphicAssets.useGradients
                        ? `linear-gradient(135deg, ${brandColors.primary}, ${brandColors.secondary})`
                        : brandColors.primary,
                      color: brandColors.background,
                      boxShadow: graphicAssets.useGlowEffects 
                        ? `0 0 10px ${brandColors.primary}40`
                        : 'none',
                    }}
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    Add to Cart
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <FontPreviewer fontName={brandFonts.primary} fontWeight="400">
      <FontPreviewer fontName={brandFonts.secondary} fontWeight="400">
        <div className="h-full p-6 overflow-y-auto">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Visual Brand Preview
            </h2>

            <TypographyCard />
            <WebsiteCard />
          </div>
        </div>
      </FontPreviewer>
    </FontPreviewer>
  );
}
