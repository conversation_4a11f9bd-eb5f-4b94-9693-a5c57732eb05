'use client';

import { useEffect } from 'react';
import { Badge } from '@/components/ui/badge';
import { useBrandingStore } from '@/store/brandingStore';
import { useBrandSSE } from '@/hooks/useBrandSSE';
import { Palette, Type, Sparkles, Package, Wifi, WifiOff } from 'lucide-react';


export function BrandingDataSection() {
  const {
    brandColors,
    brandFonts,
    graphicAssets,
    productInfo,
    updateBrandColors,
    updateBrandFonts,
    updateGraphicAssets,
    updateProductInfo,
    updateSSEStatus
  } = useBrandingStore();

  const { brandData, connectionStatus, error, lastUpdated } = useBrandSSE();

  // Update store when SSE data is received
  useEffect(() => {
    if (brandData) {
      updateSSEStatus({
        connected: connectionStatus === 'connected',
        lastUpdated,
        status: connectionStatus,
        error
      });

      // Update brand data from SSE
      const data = brandData as Record<string, unknown>;
      if (data.brandColors) updateBrandColors(data.brandColors as Record<string, string>);
      if (data.brandFonts) updateBrandFonts(data.brandFonts as Record<string, string>);
      if (data.graphicAssets) updateGraphicAssets(data.graphicAssets as Record<string, boolean>);
      if (data.productInfo) updateProductInfo(data.productInfo as Record<string, unknown>);
    }
  }, [brandData, connectionStatus, error, lastUpdated, updateSSEStatus, updateBrandColors, updateBrandFonts, updateGraphicAssets, updateProductInfo]);

  return (
    <div className="bg-white border-b px-6 py-3">
      <div className="flex items-center justify-between">
        {/* Left side - Brand Data */}
        <div className="flex items-center space-x-8">
          {/* Brand Colors */}
          <div className="flex items-center space-x-2">
            <Palette className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Colors:</span>
            <div className="flex space-x-1">
              {Object.entries(brandColors).slice(0, 5).map(([key, color]) => (
                <div
                  key={key}
                  className="w-6 h-6 rounded border border-gray-200 cursor-pointer"
                  style={{ backgroundColor: color }}
                  title={`${key}: ${color}`}
                />
              ))}
            </div>
          </div>

          {/* Fonts */}
          <div className="flex items-center space-x-2">
            <Type className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Fonts:</span>
            <span className="text-sm text-gray-600">{brandFonts.primary}, {brandFonts.secondary}</span>
          </div>

          {/* Effects */}
          <div className="flex items-center space-x-2">
            <Sparkles className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Effects:</span>
            <div className="flex space-x-1">
              {Object.entries(graphicAssets).filter(([, enabled]) => enabled).slice(0, 2).map(([key]) => (
                <Badge key={key} variant="secondary" className="text-xs">
                  {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()).replace('Use ', '')}
                </Badge>
              ))}
            </div>
          </div>

          {/* Product */}
          <div className="flex items-center space-x-2">
            <Package className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Product:</span>
            <span className="text-sm text-gray-600 max-w-24 truncate">{productInfo.name}</span>
            <span className="text-sm font-semibold text-green-600">{productInfo.price}</span>
          </div>
        </div>

        {/* Right side - SSE Status */}
        <div className="flex items-center space-x-2">
          <div className="flex items-center space-x-1">
            {connectionStatus === 'connected' ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <span className="text-xs text-gray-500">
              {connectionStatus === 'connected' && lastUpdated &&
                `Updated ${Math.floor((Date.now() - lastUpdated.getTime()) / 1000)}s ago`}
              {connectionStatus === 'connecting' && 'Connecting...'}
              {connectionStatus === 'disconnected' && 'Disconnected'}
              {connectionStatus === 'error' && 'Connection Error'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
