'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useBrandingStore } from '@/store/brandingStore';
import { Send, Loader2 } from 'lucide-react';
import axios from 'axios'
import { useBrandSSE } from '@/hooks/useBrandSSE'; // <-- Add this import


export function InputSection() {
  const {
    businessInput,
    updateBusinessInput
  } = useBrandingStore();

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!businessInput.name || !businessInput.description || !businessInput.visualStyle) {
      return;
    }

    setIsSubmitting(true);

    const payload = {
      name: businessInput.name,
      description: businessInput.description,
      visualStyle: businessInput.visualStyle
    };

    try {
      // Send HTTP POST request to your Python app
      await axios.post('http://localhost:5001/trigger-python-app', payload);

      // Simulate form submission
      setTimeout(() => {
        setIsSubmitting(false);
      }, 1000);
    } 
    catch (error) {
      console.error('Error triggering Python app:', error);
      setIsSubmitting(false);
    }
  };

  const isFormValid = businessInput.name && businessInput.description && businessInput.visualStyle;

  const { events = [], isConnected, error } = useBrandSSE(); // <-- Use the hook

  return (
    <div className="h-full p-6 overflow-y-auto">
      <Card className="border-0 shadow-none">
        <CardHeader>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Business Information
          </CardTitle>
          <p className="text-gray-600">
            Tell us about your business to generate your brand identity
          </p>
        </CardHeader>
        
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Business Name */}
            <div className="space-y-2">
              <Label htmlFor="businessName" className="text-sm font-medium text-gray-700">
                Business Name *
              </Label>
              <Input
                id="businessName"
                placeholder="Enter your business name"
                value={businessInput.name}
                onChange={(e) => updateBusinessInput({ name: e.target.value })}
                className="w-full"
                disabled={isSubmitting}
              />
            </div>

            {/* Business Description */}
            <div className="space-y-2">
              <Label htmlFor="businessDescription" className="text-sm font-medium text-gray-700">
                Business Description *
              </Label>
              <Textarea
                id="businessDescription"
                placeholder="Describe what your business does, your target audience, and key values..."
                value={businessInput.description}
                onChange={(e) => updateBusinessInput({ description: e.target.value })}
                className="w-full min-h-[120px] resize-none"
                disabled={isSubmitting}
              />
            </div>

            {/* Visual Style Preference */}
            <div className="space-y-2">
              <Label htmlFor="visualStyle" className="text-sm font-medium text-gray-700">
                Preferred Visual Style *
              </Label>
              <Textarea
                id="visualStyle"
                placeholder="Describe your preferred style"
                value={businessInput.visualStyle}
                onChange={(e) => updateBusinessInput({ visualStyle: e.target.value })}
                className="w-full min-h-[120px] resize-none"
                disabled={isSubmitting}
              />
            </div>

            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Live Events</Label>
              <div className="bg-gray-100 rounded p-2 h-32 overflow-y-auto text-xs font-mono">
                {!isConnected && <div className="text-gray-400">Connecting...</div>}
                {error && <div className="text-red-500">Error: {error}</div>}
                {events.length === 0 && isConnected && !error && (
                  <span className="text-gray-400">No events yet.</span>
                )}
                {events.map((e, i) => (
                  <div key={i}>{e}</div>
                ))}
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full"
              disabled={!isFormValid || isSubmitting}
              size="lg"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Brand Identity...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Generate Brand Identity
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
