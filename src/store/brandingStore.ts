import { create } from 'zustand';

export interface BrandColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
}

export interface BrandFonts {
  primary: string;
  secondary: string;
}

export interface GraphicAssets {
  useGradients: boolean;
  useGlowEffects: boolean;
  useThickBorders: boolean;
}

export interface ProductInfo {
  name: string;
  price: string;
  variants: string[];
  description: string;
}

export interface BusinessInput {
  name: string;
  description: string;
  visualStyle: string;
}

export interface SSEStatus {
  connected: boolean;
  lastUpdated: Date | null;
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  error: string | null;
}

export interface BrandingState {
  // Input data
  businessInput: BusinessInput;
  
  // AI Generated data
  brandColors: BrandColors;
  brandFonts: BrandFonts;
  graphicAssets: GraphicAssets;
  productInfo: ProductInfo;
  
  // SSE status
  sseStatus: SSEStatus;

  // Actions
  updateBusinessInput: (input: Partial<BusinessInput>) => void;
  updateBrandColors: (colors: Partial<BrandColors>) => void;
  updateBrandFonts: (fonts: Partial<BrandFonts>) => void;
  updateGraphicAssets: (assets: Partial<GraphicAssets>) => void;
  updateProductInfo: (info: Partial<ProductInfo>) => void;
  updateSSEStatus: (status: Partial<SSEStatus>) => void;
  resetBrandingData: () => void;
}

const initialState = {
  businessInput: {
    name: '',
    description: '',
    visualStyle: '',
  },
  brandColors: {
    primary: '#3B82F6',
    secondary: '#8B5CF6',
    accent: '#F59E0B',
    background: '#FFFFFF',
    text: '#1F2937',
  },
  brandFonts: {
    primary: 'Inter',
    secondary: 'Roboto',
  },
  graphicAssets: {
    useGradients: true,
    useGlowEffects: false,
    useThickBorders: false,
  },
  productInfo: {
    name: 'Premium Product',
    price: '$99.99',
    variants: ['Standard', 'Premium', 'Enterprise'],
    description: 'A high-quality product designed for modern businesses',
  },
  sseStatus: {
    connected: false,
    lastUpdated: null,
    status: 'disconnected' as const,
    error: null,
  },
};

export const useBrandingStore = create<BrandingState>((set) => ({
  ...initialState,
  
  updateBusinessInput: (input) =>
    set((state) => ({
      businessInput: { ...state.businessInput, ...input },
    })),
    
  updateBrandColors: (colors) =>
    set((state) => ({
      brandColors: { ...state.brandColors, ...colors },
    })),
      
  updateBrandFonts: (fonts) =>
    set((state) => ({
      brandFonts: { ...state.brandFonts, ...fonts },
    })),
    
  updateGraphicAssets: (assets) =>
    set((state) => ({
      graphicAssets: { ...state.graphicAssets, ...assets },
    })),
    
  updateProductInfo: (info) =>
    set((state) => ({
      productInfo: { ...state.productInfo, ...info },
    })),
    
  updateSSEStatus: (status: Partial<SSEStatus>) =>
    set((state) => ({
      sseStatus: { ...state.sseStatus, ...status },
    })),
    
  resetBrandingData: () => set(initialState),
}));
